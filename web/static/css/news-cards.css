/* Enhanced News Cards Styling */

/* News Container */
.news-enhanced-container {
    padding: 1.5rem 0;
}

/* News Filters Section */
.news-filters {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.news-filters h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

.filter-group {
    margin-bottom: 1rem;
}

.filter-group label {
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.filter-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* News Grid */
.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* News Card */
.news-card {
    background: #ffffff;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.news-card-header {
    padding: 1.25rem 1.25rem 0;
    position: relative;
}

.news-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.news-source {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.news-date {
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 500;
}

.news-card-title {
    margin: 0 0 1rem 0;
    line-height: 1.4;
}

.news-card-title a {
    color: #212529;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: color 0.2s ease;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-card-title a:hover {
    color: #007bff;
}

.news-card-body {
    padding: 0 1.25rem 1.25rem;
}

.news-summary {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Sentiment Badge */
.sentiment-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sentiment-positive {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.sentiment-negative {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
    color: white;
}

.sentiment-neutral {
    background: linear-gradient(135deg, #6c757d, #adb5bd);
    color: white;
}

/* News Card Footer */
.news-card-footer {
    padding: 0.75rem 1.25rem;
    background: rgba(248, 249, 250, 0.5);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.news-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.news-tag {
    background: #e9ecef;
    color: #495057;
    padding: 0.2rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.7rem;
    font-weight: 500;
}

.news-actions {
    display: flex;
    gap: 0.5rem;
}

.news-action-btn {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 1rem;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 0.25rem;
}

.news-action-btn:hover {
    color: #007bff;
}

/* Pagination */
.news-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.9rem;
}

.load-more-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.load-more-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Search and Sort */
.news-search-sort {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.search-input-group {
    flex: 1;
    min-width: 250px;
    position: relative;
}

.search-input-group input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 2px solid #e9ecef;
    border-radius: 2rem;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.search-input-group input:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.search-input-group .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.sort-dropdown {
    min-width: 150px;
}

.sort-dropdown select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 2rem;
    font-size: 0.9rem;
    background: white;
    cursor: pointer;
}

/* Loading States */
.news-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
    flex-direction: column;
    gap: 1rem;
}

.news-loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid #e9ecef;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty State */
.news-empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.news-empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .news-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .news-filters {
        padding: 1rem;
    }
    
    .filter-actions {
        flex-direction: column;
    }
    
    .filter-actions .btn {
        width: 100%;
    }
    
    .news-search-sort {
        flex-direction: column;
    }
    
    .search-input-group,
    .sort-dropdown {
        min-width: auto;
    }
    
    .news-card-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .sentiment-badge {
        position: static;
        align-self: flex-end;
        margin-top: 0.5rem;
    }
}

@media (max-width: 576px) {
    .news-enhanced-container {
        padding: 1rem 0;
    }
    
    .news-card {
        border-radius: 0.75rem;
    }
    
    .news-card-header,
    .news-card-body {
        padding: 1rem;
    }
    
    .news-card-footer {
        padding: 0.75rem 1rem;
    }
}

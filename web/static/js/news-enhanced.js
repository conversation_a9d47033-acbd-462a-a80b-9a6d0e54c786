// Enhanced News Display Functionality

class NewsManager {
    constructor() {
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.totalItems = 0;
        this.allArticles = [];
        this.filteredArticles = [];
        this.isLoading = false;
        this.filters = {
            search: '',
            source: '',
            sentiment: '',
            startDate: '',
            endDate: '',
            sortBy: 'date',
            sortOrder: 'desc'
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setDefaultDates();
        this.loadNews();
    }
    
    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('news-search-enhanced');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce((e) => {
                this.filters.search = e.target.value.trim();
                this.applyFilters();
            }, 300));
        }
        
        // Sort dropdown
        const sortSelect = document.getElementById('news-sort');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                const [sortBy, sortOrder] = e.target.value.split('-');
                this.filters.sortBy = sortBy;
                this.filters.sortOrder = sortOrder;
                this.applyFilters();
            });
        }
        
        // Filter form
        const filterForm = document.getElementById('news-filter-form');
        if (filterForm) {
            filterForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.applyFormFilters();
            });
        }
        
        // Clear filters button
        const clearFiltersBtn = document.getElementById('clear-filters-enhanced');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }
        
        // Load more button
        document.addEventListener('click', (e) => {
            if (e.target.id === 'load-more-enhanced') {
                this.loadMore();
            }
        });
    }
    
    setDefaultDates() {
        const today = new Date();
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(today.getDate() - 7);
        
        const startDateInput = document.getElementById('news-start-date-enhanced');
        const endDateInput = document.getElementById('news-end-date-enhanced');
        
        if (startDateInput) {
            startDateInput.value = this.formatDateForInput(sevenDaysAgo);
            this.filters.startDate = startDateInput.value;
        }
        
        if (endDateInput) {
            endDateInput.value = this.formatDateForInput(today);
            this.filters.endDate = endDateInput.value;
        }
    }
    
    formatDateForInput(date) {
        return date.toISOString().split('T')[0];
    }
    
    formatDateForDisplay(dateStr) {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    async loadNews() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const params = new URLSearchParams();
            
            if (this.filters.startDate) params.append('start_date', this.filters.startDate);
            if (this.filters.endDate) params.append('end_date', this.filters.endDate);
            if (this.filters.search) params.append('search', this.filters.search);
            
            params.append('limit', '100'); // Load more articles for better filtering
            
            const response = await fetch(`/api/news?${params.toString()}`);
            
            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            this.allArticles = data.all || [];
            this.applyFilters();
            
        } catch (error) {
            console.error('Error loading news:', error);
            this.showError(error.message);
        } finally {
            this.isLoading = false;
        }
    }
    
    applyFilters() {
        let filtered = [...this.allArticles];
        
        // Apply search filter
        if (this.filters.search) {
            const searchTerm = this.filters.search.toLowerCase();
            filtered = filtered.filter(article => 
                article.title.toLowerCase().includes(searchTerm) ||
                (article.content && article.content.toLowerCase().includes(searchTerm))
            );
        }
        
        // Apply source filter
        if (this.filters.source) {
            filtered = filtered.filter(article => article.source === this.filters.source);
        }
        
        // Apply sentiment filter
        if (this.filters.sentiment) {
            filtered = filtered.filter(article => {
                const sentiment = this.getArticleSentiment(article);
                return sentiment === this.filters.sentiment;
            });
        }
        
        // Apply sorting
        filtered.sort((a, b) => {
            let aValue, bValue;
            
            switch (this.filters.sortBy) {
                case 'date':
                    aValue = new Date(a.date || a.publish_time);
                    bValue = new Date(b.date || b.publish_time);
                    break;
                case 'title':
                    aValue = a.title.toLowerCase();
                    bValue = b.title.toLowerCase();
                    break;
                case 'source':
                    aValue = a.source.toLowerCase();
                    bValue = b.source.toLowerCase();
                    break;
                default:
                    aValue = new Date(a.date || a.publish_time);
                    bValue = new Date(b.date || b.publish_time);
            }
            
            if (this.filters.sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
        
        this.filteredArticles = filtered;
        this.totalItems = filtered.length;
        this.currentPage = 1;
        this.renderNews();
    }
    
    applyFormFilters() {
        // Get form values
        const startDate = document.getElementById('news-start-date-enhanced')?.value || '';
        const endDate = document.getElementById('news-end-date-enhanced')?.value || '';
        const source = document.getElementById('news-source-enhanced')?.value || '';
        const sentiment = document.getElementById('news-sentiment-enhanced')?.value || '';
        
        // Update filters
        this.filters.startDate = startDate;
        this.filters.endDate = endDate;
        this.filters.source = source;
        this.filters.sentiment = sentiment;
        
        // Reload news with new date filters
        this.loadNews();
    }
    
    clearFilters() {
        // Reset form inputs
        const searchInput = document.getElementById('news-search-enhanced');
        const sourceSelect = document.getElementById('news-source-enhanced');
        const sentimentSelect = document.getElementById('news-sentiment-enhanced');
        const sortSelect = document.getElementById('news-sort');
        
        if (searchInput) searchInput.value = '';
        if (sourceSelect) sourceSelect.value = '';
        if (sentimentSelect) sentimentSelect.value = '';
        if (sortSelect) sortSelect.value = 'date-desc';
        
        // Reset filters
        this.filters = {
            search: '',
            source: '',
            sentiment: '',
            startDate: this.filters.startDate, // Keep date filters
            endDate: this.filters.endDate,
            sortBy: 'date',
            sortOrder: 'desc'
        };
        
        this.applyFilters();
    }
    
    getArticleSentiment(article) {
        // Extract sentiment from article metadata
        if (article.article_metadata && article.article_metadata.sentiment) {
            return article.article_metadata.sentiment;
        }
        
        // Fallback to neutral if no sentiment data
        return 'neutral';
    }
    
    getSentimentClass(sentiment) {
        switch (sentiment.toLowerCase()) {
            case 'positive': return 'sentiment-positive';
            case 'negative': return 'sentiment-negative';
            default: return 'sentiment-neutral';
        }
    }
    
    createArticleCard(article) {
        const sentiment = this.getArticleSentiment(article);
        const sentimentClass = this.getSentimentClass(sentiment);
        const publishDate = this.formatDateForDisplay(article.date || article.publish_time);
        const summary = article.content ? article.content.substring(0, 150) + '...' : '';
        
        return `
            <div class="news-card" data-article-id="${article.id}">
                <div class="news-card-header">
                    <div class="news-card-meta">
                        <span class="news-source">${article.source || 'Unknown'}</span>
                        <span class="news-date">${publishDate}</span>
                    </div>
                    <div class="sentiment-badge ${sentimentClass}">
                        ${sentiment}
                    </div>
                    <h3 class="news-card-title">
                        <a href="${article.url}" target="_blank" rel="noopener noreferrer">
                            ${article.title}
                        </a>
                    </h3>
                </div>
                <div class="news-card-body">
                    ${summary ? `<p class="news-summary">${summary}</p>` : ''}
                </div>
                <div class="news-card-footer">
                    <div class="news-tags">
                        ${article.tags ? article.tags.slice(0, 3).map(tag => 
                            `<span class="news-tag">${tag}</span>`
                        ).join('') : ''}
                    </div>
                    <div class="news-actions">
                        <button class="news-action-btn" title="Share">
                            <i class="bi bi-share"></i>
                        </button>
                        <button class="news-action-btn" title="Bookmark">
                            <i class="bi bi-bookmark"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    renderNews() {
        const container = document.getElementById('news-grid-container');
        if (!container) return;
        
        if (this.filteredArticles.length === 0) {
            container.innerHTML = `
                <div class="news-empty-state">
                    <i class="bi bi-newspaper"></i>
                    <h4>No articles found</h4>
                    <p>Try adjusting your filters or search terms.</p>
                </div>
            `;
            return;
        }
        
        const startIndex = 0;
        const endIndex = this.currentPage * this.itemsPerPage;
        const articlesToShow = this.filteredArticles.slice(startIndex, endIndex);
        
        const articlesHtml = articlesToShow.map(article => this.createArticleCard(article)).join('');
        
        container.innerHTML = `
            <div class="news-grid">
                ${articlesHtml}
            </div>
            ${this.renderPagination()}
        `;
    }
    
    renderPagination() {
        const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        const hasMore = this.currentPage < totalPages;
        const showingCount = Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
        
        return `
            <div class="news-pagination">
                <div class="pagination-info">
                    Showing ${showingCount} of ${this.totalItems} articles
                </div>
                ${hasMore ? `
                    <button id="load-more-enhanced" class="load-more-btn">
                        Load More Articles
                    </button>
                ` : ''}
            </div>
        `;
    }
    
    loadMore() {
        if (this.isLoading) return;
        
        this.currentPage++;
        this.renderNews();
    }
    
    showLoading() {
        const container = document.getElementById('news-grid-container');
        if (container) {
            container.innerHTML = `
                <div class="news-loading">
                    <div class="news-loading-spinner"></div>
                    <p>Loading articles...</p>
                </div>
            `;
        }
    }
    
    showError(message) {
        const container = document.getElementById('news-grid-container');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <h5>Error loading news</h5>
                    <p>${message}</p>
                    <button class="btn btn-primary" onclick="newsManager.loadNews()">
                        Try Again
                    </button>
                </div>
            `;
        }
    }
}

// Initialize the news manager when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.newsManager = new NewsManager();
});

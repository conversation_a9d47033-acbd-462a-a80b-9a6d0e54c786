<!-- Enhanced News Filters Component -->
<div class="news-filters">
    <h5><i class="bi bi-funnel"></i> Filter News</h5>
    
    <form id="news-filter-form" class="row g-3">
        <!-- Date Range Filters -->
        <div class="col-md-6">
            <div class="filter-group">
                <label for="news-start-date-enhanced" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="news-start-date-enhanced" name="start_date">
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="filter-group">
                <label for="news-end-date-enhanced" class="form-label">End Date</label>
                <input type="date" class="form-control" id="news-end-date-enhanced" name="end_date">
            </div>
        </div>
        
        <!-- Source Filter -->
        <div class="col-md-6">
            <div class="filter-group">
                <label for="news-source-enhanced" class="form-label">Source</label>
                <select class="form-select" id="news-source-enhanced" name="source">
                    <option value="">All Sources</option>
                    <option value="Reuters">Reuters</option>
                    <option value="Bloomberg">Bloomberg</option>
                    <option value="MarketWatch">MarketWatch</option>
                    <option value="Yahoo Finance">Yahoo Finance</option>
                    <option value="CNBC">CNBC</option>
                    <option value="Financial Times">Financial Times</option>
                    <option value="Wall Street Journal">Wall Street Journal</option>
                </select>
            </div>
        </div>
        
        <!-- Sentiment Filter -->
        <div class="col-md-6">
            <div class="filter-group">
                <label for="news-sentiment-enhanced" class="form-label">Sentiment</label>
                <select class="form-select" id="news-sentiment-enhanced" name="sentiment">
                    <option value="">All Sentiments</option>
                    <option value="positive">Positive</option>
                    <option value="neutral">Neutral</option>
                    <option value="negative">Negative</option>
                </select>
            </div>
        </div>
        
        <!-- Filter Actions -->
        <div class="col-12">
            <div class="filter-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i> Apply Filters
                </button>
                <button type="button" class="btn btn-outline-secondary" id="clear-filters-enhanced">
                    <i class="bi bi-x-circle"></i> Clear Filters
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Search and Sort Section -->
<div class="news-search-sort">
    <div class="search-input-group">
        <i class="bi bi-search search-icon"></i>
        <input 
            type="text" 
            id="news-search-enhanced" 
            class="form-control" 
            placeholder="Search articles by title or content..."
            autocomplete="off"
        >
    </div>
    
    <div class="sort-dropdown">
        <select class="form-select" id="news-sort">
            <option value="date-desc">Newest First</option>
            <option value="date-asc">Oldest First</option>
            <option value="title-asc">Title A-Z</option>
            <option value="title-desc">Title Z-A</option>
            <option value="source-asc">Source A-Z</option>
            <option value="source-desc">Source Z-A</option>
        </select>
    </div>
</div>

{% extends "layout.html" %}

{% block title %}Market Monitor{% endblock %}

{% block content %}
<div class="row mb-4">
    <!-- Price Chart Section (Yahoo Finance Style) -->
    <div class="col-12">
        <div class="card" style="min-height: 500px;">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 me-2">S&P 500 Index (SPY)</h5>
                    <span class="badge bg-secondary">US Market</span>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Chart Type Selection -->
                    <div class="btn-group me-3" role="group">
                        <button id="btn-line" class="btn btn-sm btn-outline-secondary active">Line</button>
                        <button id="btn-candle" class="btn btn-sm btn-outline-secondary">Candle</button>
                        <button id="btn-area" class="btn btn-sm btn-outline-secondary">Area</button>
                    </div>
                    <!-- Time Period Selection -->
                    <div class="btn-group" role="group">
                        <button id="btn-1m" class="btn btn-sm btn-outline-primary">1M</button>
                        <button id="btn-3m" class="btn btn-sm btn-outline-primary">3M</button>
                        <button id="btn-6m" class="btn btn-sm btn-outline-primary">6M</button>
                        <button id="btn-1y" class="btn btn-sm btn-outline-primary">1Y</button>
                        <button id="btn-ytd" class="btn btn-sm btn-outline-primary">YTD</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Price Summary Row (Yahoo Finance Style) -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div id="price-summary" class="px-2 pt-2">
                            <div class="row">
                                <!-- Main Price Display -->
                                <div class="col-md-3">
                                    <div class="d-flex align-items-baseline">
                                        <h3 id="current-price" class="me-2 mb-0 fw-bold">--</h3>
                                        <span id="price-change" class="fs-5 fw-bold">--</span>
                                    </div>
                                    <div class="text-muted small" id="last-updated" style="font-size: 0.8rem;">As of --
                                    </div>
                                </div>

                                <!-- Key Statistics -->
                                <div class="col-md-9">
                                    <div class="row">
                                        <div class="col-md-2 col-4 mb-2">
                                            <div class="small text-muted fw-bold">Previous Close</div>
                                            <div id="prev-close" class="small fw-bold">--</div>
                                        </div>
                                        <div class="col-md-2 col-4 mb-2">
                                            <div class="small text-muted fw-bold">Open</div>
                                            <div id="open-price" class="small fw-bold">--</div>
                                        </div>
                                        <div class="col-md-2 col-4 mb-2">
                                            <div class="small text-muted fw-bold">Day's Range</div>
                                            <div id="day-range" class="small fw-bold">--</div>
                                        </div>
                                        <div class="col-md-2 col-4 mb-2">
                                            <div class="small text-muted fw-bold">52 Week Range</div>
                                            <div id="year-range" class="small fw-bold">--</div>
                                        </div>
                                        <div class="col-md-2 col-4 mb-2">
                                            <div class="small text-muted fw-bold">Volume</div>
                                            <div id="volume" class="small fw-bold">--</div>
                                        </div>
                                        <div class="col-md-2 col-4 mb-2">
                                            <div class="small text-muted fw-bold">Avg. Volume</div>
                                            <div id="avg-volume" class="small fw-bold">--</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Chart Row -->
                <div class="row">
                    <div class="col-12">
                        <!-- Chart section -->
                        <div id="sp500-graph" class="px-1" style="height: 350px;"></div>
                        <div id="loading-indicator" class="text-center mt-5 d-none">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading data...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Prediction Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h5 class="mb-0">Market Prediction</h5>
                    <div class="d-flex align-items-center">
                        <!-- LLM API Selection -->
                        <div class="me-3">
                            <select id="llm-api-select" class="form-select form-select-sm" style="width: auto;">
                                <option value="gemini" selected>Gemini</option>
                                <option value="openai">OpenAI</option>
                                <option value="anthropic">Anthropic</option>
                            </select>
                        </div>
                        <!-- Prediction Type Toggle -->
                        <div class="btn-group me-3" role="group">
                            <input type="radio" class="btn-check" name="prediction-type" id="show-traditional" checked>
                            <label class="btn btn-outline-secondary btn-sm" for="show-traditional">
                                <i class="bi bi-calculator me-1"></i>Traditional
                            </label>
                            <input type="radio" class="btn-check" name="prediction-type" id="show-llm">
                            <label class="btn btn-outline-secondary btn-sm" for="show-llm">
                                <i class="bi bi-robot me-1"></i>LLM Analysis
                            </label>
                            <input type="radio" class="btn-check" name="prediction-type" id="show-both">
                            <label class="btn btn-outline-secondary btn-sm" for="show-both">
                                <i class="bi bi-columns-gap me-1"></i>Both
                            </label>
                        </div>
                        <!-- Update Button -->
                        <button id="run-prediction-btn-main" class="btn btn-sm btn-primary">
                            <i class="bi bi-arrow-clockwise me-1"></i>Update Predictions
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Traditional Model Prediction -->
                <div id="traditional-prediction-section" class="prediction-section">
                    <div class="d-flex align-items-center mb-3">
                        <i class="bi bi-calculator text-primary me-2"></i>
                        <h6 class="mb-0 text-primary">Traditional Model Prediction</h6>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <!-- Main Prediction -->
                            <div class="d-flex flex-column">
                                <h6 class="text-muted mb-2">Prediction for Next Closing Price Trend:</h6>
                                <div id="prediction-main" class="mb-3">
                                    <div class="placeholder-glow">
                                        <span class="placeholder col-8"></span>
                                    </div>
                                </div>

                                <!-- Confidence Score Bars -->
                                <h6 class="text-muted mb-2">Confidence Scores</h6>
                                <div id="prediction-confidence" class="mb-3">
                                    <div class="placeholder-glow">
                                        <span class="placeholder col-12"></span>
                                        <span class="placeholder col-12 mt-2"></span>
                                        <span class="placeholder col-12 mt-2"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-8">
                            <!-- Influential Articles -->
                            <h6 class="text-muted mb-2">Influential Articles</h6>
                            <div id="prediction-articles">
                                <div class="placeholder-glow">
                                    <span class="placeholder col-12" style="height: 100px;"></span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-3 d-flex justify-content-end">
                                <button id="view-history-btn-main" class="btn btn-outline-secondary btn-sm me-2">
                                    <i class="bi bi-graph-up me-1"></i>View Historical Accuracy
                                </button>
                                <button id="view-all-articles-btn" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-newspaper me-1"></i>View All Influential Articles
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- LLM Prediction Section -->
                <div id="llm-prediction-section" class="prediction-section" style="display: none;">
                    <div class="d-flex align-items-center mb-3">
                        <i class="bi bi-robot text-info me-2"></i>
                        <h6 class="mb-0 text-info">LLM Analysis</h6>
                        <span id="llm-api-badge" class="badge bg-info ms-2">Gemini</span>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <!-- LLM Main Prediction -->
                            <div class="d-flex flex-column">
                                <h6 class="text-muted mb-2">Market Direction Prediction:</h6>
                                <div id="llm-prediction-main" class="mb-3">
                                    <div class="placeholder-glow">
                                        <span class="placeholder col-8"></span>
                                    </div>
                                </div>

                                <!-- LLM Confidence Score Bars -->
                                <h6 class="text-muted mb-2">Confidence Scores</h6>
                                <div id="llm-prediction-confidence" class="mb-3">
                                    <div class="placeholder-glow">
                                        <span class="placeholder col-12"></span>
                                        <span class="placeholder col-12 mt-2"></span>
                                        <span class="placeholder col-12 mt-2"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-8">
                            <!-- Key Evidence -->
                            <h6 class="text-muted mb-2">Key Evidence</h6>
                            <div id="llm-key-evidence" class="mb-3">
                                <div class="placeholder-glow">
                                    <span class="placeholder col-12" style="height: 60px;"></span>
                                </div>
                            </div>

                            <!-- Dominant Theme -->
                            <h6 class="text-muted mb-2">Dominant Market Theme</h6>
                            <div id="llm-dominant-theme" class="mb-3">
                                <div class="placeholder-glow">
                                    <span class="placeholder col-10"></span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-3 d-flex justify-content-end">
                                <button id="view-llm-outlook-btn" class="btn btn-outline-info btn-sm me-2">
                                    <i class="bi bi-eye me-1"></i>Detailed Outlook
                                </button>
                                <button id="view-critical-levels-btn" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-bar-chart me-1"></i>Critical Levels
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- News Section -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h5 class="mb-0">Financial News</h5>
                    <div class="d-flex align-items-center">
                        <div class="input-group input-group-sm me-2" style="width: auto;">
                            <span class="input-group-text">From</span>
                            <input type="date" class="form-control" id="news-start-date">
                        </div>
                        <div class="input-group input-group-sm me-2" style="width: auto;">
                            <span class="input-group-text">To</span>
                            <input type="date" class="form-control" id="news-end-date">
                        </div>
                        <div class="input-group input-group-sm me-2" style="width: 200px;">
                            <input type="text" class="form-control" id="news-search" placeholder="Search news...">
                            <button class="btn btn-outline-secondary" type="button" id="clear-search-btn">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                        <button id="filter-news-btn" class="btn btn-sm btn-primary me-1">Filter</button>
                        <button id="clear-news-filter-btn" class="btn btn-sm btn-outline-secondary">View Latest</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="news-container">
                    <p class="text-center text-muted">Loading latest financial news...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Define a global object to store shared functions -->
<script>
    // Create a global object to store shared functions
    window.NewsMonitor = {
        // This will be populated by the individual modules
        fetchAvailableDates: null,
        loadNewsForDate: null,
        updateCrawlerStatus: null,
        toggleCrawlerStatus: null,
        loadPrediction: null,
        // Function to update the prediction
        refreshPrediction: function (currentPrice) {
            console.log('refreshPrediction called with price:', currentPrice);
            // If loadPrediction is defined, call it
            if (typeof this.loadPrediction === 'function') {
                console.log('Calling loadPrediction function');
                this.loadPrediction(currentPrice);
            } else {
                console.warn('loadPrediction function not available');
            }
        }
    };
</script>

<!-- Load scripts in the correct order -->
<script src="{{ url_for('static', filename='js/news.js') }}"></script>
<script src="{{ url_for('static', filename='js/graph.js') }}"></script>
<script src="{{ url_for('static', filename='js/prediction.js') }}"></script>

<!-- Add a script to check for errors -->
<script>
    window.addEventListener('error', function (e) {
        console.error('JavaScript error:', e.message, 'at', e.filename, 'line', e.lineno);
        alert('An error occurred: ' + e.message);
    });

    // Check if Plotly is loaded
    if (typeof Plotly === 'undefined') {
        console.error('Plotly is not loaded!');
        alert('Plotly library is not loaded. Please check your internet connection.');
    }

    // Ensure graph is properly sized when the page loads
    window.addEventListener('load', function () {
        // Force a resize event after the page is fully loaded
        window.dispatchEvent(new Event('resize'));

        // Also trigger a resize after a short delay to ensure everything is rendered
        setTimeout(function () {
            window.dispatchEvent(new Event('resize'));
        }, 1000);
    });
</script>
{% endblock %}
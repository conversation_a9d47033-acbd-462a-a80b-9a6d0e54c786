# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html

from datetime import timedelta
import traceback
from typing import List, Optional
from itemadapter import ItemAdapter
import logging
from db.database import DatabaseManager
from news_crawler.settings import DATE_FORMAT
from nlp.utils import split_text_into_chunks
from nlp.llm.llm_analyzer import LLMAnalyzer


class DBWriterPipeline:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.info("PGSQLWriterPipeline initialized")
        self.db = None

    def open_spider(self, spider):
        self.logger.info("Opening PostgreSQL database connection...")
        self.db = DatabaseManager()

    def close_spider(self, spider):
        self.logger.info("Closing PostgreSQL database connection...")
        if self.db:
            self.db.close()

    @classmethod
    def from_crawler(cls, crawler):
        return cls()

    def process_item(self, item, spider):
        # Check if item is None
        if item is None:
            return None

        adapter = ItemAdapter(item)
        article = {
            'title': adapter.get('title'),
            'content': adapter.get('content'),
            'url': adapter.get('url'),
            'author': adapter.get('author'),
            'source': adapter.get('source'),
            'publish_time': adapter.get('publish_time'),
            'update_time': adapter.get('update_time'),
            'date': adapter.get('update_time') or adapter.get('publish_time'),
            'tags': adapter.get('tags'),
            'crawl_time': adapter.get('crawl_time')
        }
        article['article_metadata'] = {
            'sentiment_analysis': adapter.get('sentiment_analysis'),
            'indicator_analysis': adapter.get('indicator_analysis')
        }
        article['article_metadata'].update(
            adapter.get('llm_analysis', {}))

        chunks = []
        for ch in adapter.get('chunks'):
            metadata = ch.get('metadata', {})
            chunk = {
                'article_date': article['date'],
                'article_title': article['title'],
                'article_url': article['url'],
                'type': metadata.get('type'),
                'chunk_index': metadata.get('chunk_idx'),
                'text': ch.get('document'),
                'chunk_metadata': {}
            }
            if 'sentiment_analysis' in metadata:
                chunk['chunk_metadata']['sentiment_analysis'] = metadata['sentiment_analysis']
            if 'indicator_analysis' in metadata:
                chunk['chunk_metadata']['indicator_analysis'] = metadata['indicator_analysis']
            chunks.append(chunk)

        try:
            article_id = self.db.article_service.upsert_article_with_chunks(
                article, chunks)
            self.logger.info(
                f"Successfully inserted article {article_id} to db")
        except Exception as e:
            self.logger.error(f"Error saving article to database: {e}")

        return item


class ChunkingPipeline:
    """
    Pipeline to chunk articles into smaller chunks.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.info("ChunkingPipeline initialized")

    @classmethod
    def from_crawler(cls, crawler):
        return cls()

    def process_item(self, item, spider):
        # Check if item is None
        if item is None:
            return None

        adapter = ItemAdapter(item)

        chunks = []

        # Add title as a chunk if present
        title = adapter.get('title', '')
        if title:
            chunks.append({
                'document': title,
                'metadata': {
                    'type': 'title',
                    'word_count': len(title.split()),
                    'chunk_idx': 0
                },
            })

        # Add content chunks if present
        content = adapter.get('content')
        if content:
            content_chunks = split_text_into_chunks(content)
            for idx, chunk in enumerate(content_chunks):
                chunks.append({
                    'document': chunk,
                    'metadata': {
                        'type': 'content',
                        'word_count': len(chunk.split()),
                        'chunk_idx': idx + 1
                    },
                })

        adapter['chunks'] = chunks
        self.logger.info(
            f"Splitted article: {title}, into {len(chunks)} chunks")

        return item


class ArticleAgeFilterPipeline:
    """
    Pipeline to filter out articles that are:
    1. Older than the specified start date (if provided)
    """

    def __init__(self, start_date=None, max_backtrack_days=365):
        self.logger = logging.getLogger(__name__)
        from datetime import datetime as dt

        max_backtrack_date_dt = dt.now() - timedelta(days=max_backtrack_days)
        self.start_date_dt = max_backtrack_date_dt

        if start_date is not None:
            start_date_dt = dt.strptime(start_date, DATE_FORMAT)
            if start_date_dt > max_backtrack_date_dt:
                self.start_date_dt = start_date_dt

        self.start_date_dt = self.start_date_dt.replace(
            tzinfo=dt.now().astimezone().tzinfo)
        self.logger.info(f"Filtering articles older than {self.start_date_dt}")

    @classmethod
    def from_crawler(cls, crawler):
        # Get the start_date from settings
        start_date = crawler.settings.get('START_DATE')
        max_backtrack_days = crawler.settings.get('MAX_BACKTRACK_DAYS')
        return cls(start_date, max_backtrack_days)

    def process_item(self, item, spider):
        # Check if item is None
        if item is None:
            return None

        adapter = ItemAdapter(item)
        article_time = adapter.get(
            'update_time') or adapter.get('publish_time')

        if article_time:
            try:
                from datetime import datetime as dt

                # Parse ISO format date
                parsed_date = dt.fromisoformat(
                    article_time.replace('Z', '+00:00'))

                # Check if article is older than the start date
                if self.start_date_dt and parsed_date < self.start_date_dt:
                    self.logger.info(
                        f"Filtering out old article: {adapter.get('url')} on {parsed_date}")
                    return None

            except Exception as e:
                self.logger.error(
                    f"Error checking article age for {adapter.get('url')}: {e}")

        return item


class SentimentAnalysisPipeline:
    """
    Pipeline to analyze articles using the SentimentAnalyzer for multiple tasks.
    This allows the web server to directly access analysis results without a separate batch process.

    Features:
    1. Uses SentimentAnalyzer with configurable executors for different tasks
    2. Processes each article to get prediction scores for each task
    3. Adds analysis results to the article item for each task
    4. Supports chunk-based analysis for detailed results
    5. Handles errors gracefully with appropriate logging
    """

    def __init__(self, tasks: List[str]):
        self.logger = logging.getLogger(__name__)
        # Initialize analyzer with executors
        try:
            from nlp.sentiment.sentiment_analyzer import SentimentAnalyzer
            self.analyzer = SentimentAnalyzer(tasks)
            self.logger.info(
                f"SentimentAnalyzer initialized successfully with all tasks: {[task for task in tasks]}")
        except Exception as e:
            self.logger.error(f"Error initializing SentimentAnalyzer: {e}")
            self.analyzer = None

    @classmethod
    def from_crawler(cls, crawler):
        # Get settings from crawler
        tasks = crawler.settings.getlist('ANALYSIS_TASKS', None)
        return cls(tasks)

    def process_item(self, item, spider):
        # Check if item is None
        if item is None:
            return None

        # Check if analyzer is available
        if self.analyzer is None:
            self.logger.warning(
                "No SentimentAnalyzer available. Skipping analysis.")
            return item

        # Convert to dictionary if it's an ItemAdapter
        article_dict = dict(ItemAdapter(item))
        item_adapter = ItemAdapter(item)

        try:
            analysis_results, chunks = self.analyzer.analyze_article_with_chunks(
                article_dict)
            self.logger.debug(f"Analysis result: {analysis_results}")
            for task_name in self.analyzer.operators.keys():
                item_adapter[f"{task_name}_analysis"] = analysis_results[task_name]
            item_adapter['chunks'] = chunks

        except Exception as e:
            self.logger.error(f"Error analyzing article: {e}")
            traceback.print_exc()

        return item


class CleanTextPipeline:
    """
    Pipeline to clean and normalize text fields
    """

    def __init__(self, max_words: Optional[int] = None):
        self.logger = logging.getLogger(__name__)
        self.max_words = max_words
        self.logger.info("CleanTextPipeline initialized")

    @classmethod
    def from_crawler(cls, crawler):
        # Get settings from crawler
        max_words = crawler.settings.getint('MAX_WORDS_ARTICLE_CONTENT', None)
        return cls(max_words)

    def process_item(self, item, spider):
        # Check if item is None
        if item is None:
            return None

        adapter = ItemAdapter(item)

        # Clean text fields
        text_fields = ['title', 'content', 'author']
        for field in text_fields:
            if field in adapter:
                text = adapter.get(field)
                if text:
                    # Remove extra whitespace
                    cleaned_text = ' '.join(text.split())
                    adapter[field] = cleaned_text
                else:
                    # Replace None with empty string
                    adapter[field] = ""
            else:
                # Add missing field with empty string
                adapter[field] = ""

        # Truncate content to max words
        if self.max_words and adapter.get('content'):
            truncated_content = split_text_into_chunks(
                adapter.get('content'), self.max_words, respect_sentences=True)[0]
            if truncated_content != adapter.get('content'):
                self.logger.info(
                    f"Truncated content for {adapter.get('url')} to {self.max_words} words")
                adapter['content'] = truncated_content

        return item


class LlmAnalyzerPipeline:
    """
    Pipeline to analyze articles using LLM analyzers.

    This pipeline integrates LLM-based analysis into the Scrapy pipeline,
    allowing articles to be analyzed during the crawling process.
    """

    def __init__(self, api_name: str, model: str, prompt_type: str, total_budget: float):
        self.logger = logging.getLogger(__name__)
        # Initialize LLM analyzer
        try:
            self.prompt_type = prompt_type
            self.analyzer = LLMAnalyzer(
                api_name, model, prompt_type, total_budget)
            self.logger.info(
                f"LlmAnalyzerPipeline initialized with {api_name} API and {prompt_type} prompt")
        except Exception as e:
            self.logger.error(f"Error initializing LLMAnalyzer: {e}")
            self.analyzer = None

    @classmethod
    def from_crawler(cls, crawler):
        # Get settings from crawler
        api_name = crawler.settings.get('LLM_API_NAME', 'gemini')
        model = crawler.settings.get('LLM_MODEL', 'gemini-2.0-flash-001')
        prompt_type = crawler.settings.get(
            'LLM_PROMPT_TYPE', 'influence_tagging')
        total_budget = crawler.settings.getfloat('LLM_TOTAL_BUDGET', 5.0)

        return cls(
            api_name=api_name,
            model=model,
            prompt_type=prompt_type,
            total_budget=total_budget
        )

    def process_item(self, item, spider):
        # Check if item is None
        if item is None:
            return None

        # Check if analyzer is available
        if self.analyzer is None:
            self.logger.warning(
                "No LLMAnalyzer available. Skipping LLM analysis.")
            return item

        adapter = ItemAdapter(item)

        try:
            # Convert item to article dictionary format expected by analyze_article
            article = {
                # Use URL as ID if no specific ID field
                'id': adapter.get('url'),
                'title': adapter.get('title', ''),
                'content': adapter.get('content', '')
            }

            # Call analyze_article method from LLMAnalyzer
            result = self.analyzer.analyze_article(article)

            if result:
                adapter['llm_analysis'] = {self.prompt_type: result}
                self.logger.info(
                    f"Successfully analyzed article: {article['id']}")
            else:
                self.logger.warning(
                    f"Analysis failed for article: {article['id']}")

        except Exception as e:
            self.logger.error(
                f"Error in LlmAnalyzerPipeline for article {adapter.get('url', 'unknown')}: {e}")

        return item
